// Base class for user information, used in various requests
class DeviceInfo {
  final String marqueTelephone;
  final String modelTelephone;
  final String imeiTelephone;
  final String numeroSerie;

  DeviceInfo({
    required this.marqueTelephone,
    required this.modelTelephone,
    required this.imeiTelephone,
    required this.numeroSerie,
  });

  factory DeviceInfo.fromJson(Map<String, dynamic> json) {
    return DeviceInfo(
      marqueTelephone: json['marqueTelephone'],
      modelTelephone: json['modelTelephone'],
      imeiTelephone: json['imeiTelephone'],
      numeroSerie: json['numeroSerie'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'marqueTelephone': marqueTelephone,
      'modelTelephone': modelTelephone,
      'imeiTelephone': imeiTelephone,
      'numeroSerie': numeroSerie,
    };
  }
}
