


import 'package:flutter/material.dart';
import 'package:Kairos/data/enums/header_enums.dart';
import 'package:Kairos/widgets/hero_widget.dart';

// ignore: must_be_immutable
class CustomAppBar extends StatefulWidget{

  const CustomAppBar({super.key, this.pageSection = HeaderEnum.dashboard, this.title, this.onSearchTap, this.isSearchBarVisible = false});
  final HeaderEnum pageSection;
  final String? title;
  final VoidCallback? onSearchTap;
  final bool isSearchBarVisible; // State to control search bar visibility

 @override
 State<CustomAppBar> createState() => _CustomAppBarState();
}


class _CustomAppBarState extends State<CustomAppBar>{



  @override
  Widget build(BuildContext context){
    return SliverAppBar(
      pinned: true,
      forceMaterialTransparency: true,
      flexibleSpace: HeroWidget(pageSection: widget.pageSection),
      expandedHeight: 200,
      centerTitle: true,
      title: Text(widget.title ?? "", textAlign: TextAlign.center, style: TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.bold),),
      leading: IconButton(
        icon: Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => Navigator.of(context).pop(),
      ),
      actions: [
        IconButton(icon: Icon(
          widget.isSearchBarVisible? Icons.close: Icons.search, 
        color: Colors.white),
        onPressed: widget.onSearchTap,),
      ],
    );
  }
}