


import 'package:flutter/material.dart';
import 'package:Kairos/data/enums/finances_tab_enums.dart';
import 'package:Kairos/pages/finances/finances.page.dart';

class AlertWidget extends StatelessWidget {
  final String message;

  const AlertWidget({
    super.key,
    required this.message,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Colors.white,
      title: Text("ACCÉS REFUSÉ", textAlign: TextAlign.center,),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.warning_amber_outlined,
            color: Colors.red,
            size: 50,
          ),
          Text.rich(
            TextSpan(children: [
              TextSpan(
              text: "L'accés á votre espace est refusé pour le motif suivant",
              style: const TextStyle(
                color: Colors.black,
              ),
            ),
            const TextSpan(
              text: "\n",
            ),
            TextSpan(
              text: message,
              style: const TextStyle(
                color: Colors.red,
              ),
            ),
            ]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
      actionsAlignment: MainAxisAlignment.spaceEvenly,
      actionsPadding: const EdgeInsets.all(15.0),
      actionsOverflowAlignment: OverflowBarAlignment.center,
      actionsOverflowButtonSpacing: 10,
      actions: [
        
        FilledButton(
          style: ButtonStyle(
            foregroundColor: WidgetStateProperty.all(Colors.white),
            minimumSize: WidgetStateProperty.all(const Size(150, 50)),
            backgroundColor: WidgetStateProperty.all(Colors.red),
          ),
          onPressed: () {
            Navigator.of(context).pop();
            Navigator.pushNamedAndRemoveUntil(context, "/accueil", (route) => false);
            
            },
          child: const Text('DECONNEXION'),
        ),
        FilledButton(
          style: ButtonStyle(
            backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
            minimumSize: WidgetStateProperty.all(const Size(0, 50)),
          ),
          onPressed: () {
            Navigator.of(context).pop();
            Navigator.pushReplacement(context, 
            MaterialPageRoute(builder: (context) => FinancesPage(initialTab: FinancesTabEnums.fraisImpayes.value)));
          },
          child: const Text('VOIR IMPAYÉS'),
        ),
      ],
    );
  }
}