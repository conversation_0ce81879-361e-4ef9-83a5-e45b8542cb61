

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';

class CodeActivationWidget extends StatefulWidget{
  const CodeActivationWidget({super.key, required this.pageController});
  final PageController pageController;

  @override
  State<CodeActivationWidget> createState() => _CodeActivationWidgetState();
}


class _CodeActivationWidgetState extends State<CodeActivationWidget>{

  final TextEditingController _codeController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false; // Added state variable for spinner

  @override
  void dispose(){
    super.dispose();
    _codeController.dispose();
  }

  @override
  Widget build(BuildContext context){
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Text("CODE D'ACTIVATION", style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Theme.of(context).primaryColor)),
        SvgPicture.asset("assets/images/logo_kairos.svg"),
        Divider(color: Theme.of(context).primaryColor, thickness: 5, height: 20, indent: 100, endIndent: 100,),
Spacer(),
        Flexible(flex: 8, child: SvgPicture.asset("assets/images/otp_code.svg")),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: Text("Veuillez saisir le code d'activation envoyé à votre numéro de téléphone.", textAlign: TextAlign.center),
        ),
        Spacer(flex: 3),
        Form(
          key: _formKey,
          child: SizedBox(
            width: 300,
            child: TextFormField(
              controller: _codeController,
              decoration: InputDecoration(
                border: OutlineInputBorder(),
                hintText: "Code d'activation",
                hintStyle: TextStyle(color: Colors.grey.shade400),
                contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                ),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
              validator: (value){
                if(value!.isEmpty){
                  return "Veuillez saisir le code d'activation";
                } else {
                  return null;
                }
              },
            ),
            
          ),
        ),
        // Spacer(),
        SizedBox(height: 20,),
        FilledButton(
          style: ButtonStyle(
              backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
              fixedSize: WidgetStateProperty.all(Size(300, 50))),
          onPressed: _isLoading ? null : () async { // Disable button while loading
            debugPrint('the user clicked on `Continue` button');
            if(_formKey.currentState!.validate()){
              setState(() {
                _isLoading = true; // Show spinner
              });
              debugPrint('the user entered: ${_codeController.text}');

              // Simulate HTTP request delay
              await Future.delayed(Duration(seconds: 2));

              setState(() {
                _isLoading = false; // Hide spinner
              });

              widget.pageController.nextPage(duration: Duration(milliseconds: 500), curve: Curves.easeInOut);
            }
          },
          child: _isLoading
              ? SizedBox( // Show spinner when loading
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 2,
                  ),
                )
              : Text("CONTINUER", style: TextStyle(fontWeight: FontWeight.bold), ), // Show text when not loading
          ),
          // Spacer(),
          SizedBox(height: 20,),
          Text.rich(TextSpan(children: [
            TextSpan(text: "Vous n'avez pas reçu le code d'activation? "),
            TextSpan(text: "Renvoyer", style: TextStyle(color: Theme.of(context).primaryColor, fontWeight: FontWeight.bold)),
          ])),
        Spacer(flex: 3),
      ],
    );
  }
}