import 'user_info.dart';

class ActivationRequest extends UserInfo {
  final String codeUtilisateur;
  final String codeEtab;
  final String codePin;
  ActivationRequest({
    required super.numeroTelephone,
    required super.marqueTelephone,
    required super.modelTelephone,
    required super.imeiTelephone,
    required super.numeroSerie,
    required this.codeUtilisateur,
    required this.codeEtab,
    required this.codePin,
  });

  factory ActivationRequest.fromJson(Map<String, dynamic> json) {
    return ActivationRequest(
      numeroTelephone: json['numeroTelephone'],
      marqueTelephone: json['marqueTelephone'],
      modelTelephone: json['modelTelephone'],
      imeiTelephone: json['imeiTelephone'],
      numeroSerie: json['numeroSerie'],
      codeUtilisateur: json['codeUtilisateur'],
      codeEtab: json['codeEtab'],
      codePin: json['codePin'],
    );
  }

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'codeUtilisateur': codeUtilisateur,
      'codeEtab': codeEtab,
      'codePin': codePin,
    });
    return json;
  }
}
