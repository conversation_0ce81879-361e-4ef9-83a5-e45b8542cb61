class SendSmsResponse {
  final String id;
  final String numeroTelephone;
  final String marqueTelephone;
  final String modelTelephone;
  final String imeiTelephone;
  final String numeroSerie;
  final bool deleted;
  final bool activated;
  final int version;
  final DateTime dateCreated;
  final DateTime lastUpdated;

  SendSmsResponse({
    required this.id,
    required this.numeroTelephone,
    required this.marqueTelephone,
    required this.modelTelephone,
    required this.imeiTelephone,
    required this.numeroSerie,
    required this.deleted,
    required this.activated,
    required this.version,
    required this.dateCreated,
    required this.lastUpdated,
  });

  factory SendSmsResponse.fromJson(Map<String, dynamic> json) {
    return SendSmsResponse(
      id: json['id'],
      numeroTelephone: json['numeroTelephone'],
      marqueTelephone: json['marqueTelephone'],
      modelTelephone: json['modelTelephone'],
      imeiTelephone: json['imeiTelephone'],
      numeroSerie: json['numeroSerie'],
      deleted: json['deleted'],
      activated: json['activated'],
      version: json['version'],
      dateCreated: DateTime.parse(json['dateCreated']),
      lastUpdated: DateTime.parse(json['lastUpdated']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'numeroTelephone': numeroTelephone,
      'marqueTelephone': marqueTelephone,
      'modelTelephone': modelTelephone,
      'imeiTelephone': imeiTelephone,
      'numeroSerie': numeroSerie,
      'deleted': deleted,
      'activated': activated,
      'version': version,
      'dateCreated': dateCreated.toIso8601String(),
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }
}
