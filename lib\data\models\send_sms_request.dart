class SendSmsRequest {
  final String numeroTelephone;
  final String marqueTelephone;
  final String modelTelephone;
  final String imeiTelephone;
  final String numeroSerie;
  SendSmsRequest({
    required this.numeroTelephone,
    required this.marqueTelephone,
    required this.modelTelephone,
    required this.imeiTelephone,
    required this.numeroSerie,
  });

  factory SendSmsRequest.fromJson(Map<String, dynamic> json) {
    return SendSmsRequest(
      numeroTelephone: json['numeroTelephone'],
      marqueTelephone: json['marqueTelephone'],
      modelTelephone: json['modelTelephone'],
      imeiTelephone: json['imeiTelephone'],
      numeroSerie: json['numeroSerie'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'numeroTelephone': numeroTelephone,
      'marqueTelephone': marqueTelephone,
      'modelTelephone': modelTelephone,
      'imeiTelephone': imeiTelephone,
      'numeroSerie': numeroSerie,
    };
  }
}
