import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/user.dart';

/// Abstract repository interface for authentication operations
abstract class AuthRepository {
  /// Login with email and password
  Future<Either<Failure, User>> login(String email, String password);
  
  /// Register new user
  Future<Either<Failure, void>> register(String email, String password, String fullName);
  
  /// Verify PIN code
  Future<Either<Failure, void>> verifyPin(String pin);
  
  /// Logout current user
  Future<Either<Failure, void>> logout();
  
  /// Check if user is authenticated
  Future<Either<Failure, bool>> isAuthenticated();
  
  /// Get current user
  Future<Either<Failure, User>> getCurrentUser();
  
  /// Send SMS for verification
  Future<Either<Failure, void>> sendSms(String phoneNumber);
  
  /// Refresh authentication token
  Future<Either<Failure, String>> refreshToken();
  
  /// Activate user account
  Future<Either<Failure, void>> activateAccount(String activationCode);
}
