import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:Kairos/pages/accueil/accueil_widgets/app_activated.widget.dart';
import 'package:Kairos/pages/accueil/accueil_widgets/code_activation.widget.dart';
import 'package:Kairos/pages/accueil/accueil_widgets/enter_fullname.widget.dart';
import 'package:Kairos/pages/accueil/accueil_widgets/phone_authentication.widget.dart';
import 'package:Kairos/pages/accueil/accueil_widgets/home_slide.widget.dart';

class AccueilPage extends StatefulWidget{
  const AccueilPage({super.key});

  @override
  State<AccueilPage> createState() => _AccueilPageState();
}

class _AccueilPageState extends State<AccueilPage>{

  final PageController _pageController = PageController();


  @override
  Widget build(BuildContext context){
    return Scaffold(
      body: 
      Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Spacer(),
          Si<PERSON><PERSON><PERSON>(height: 40),
         Expanded(
          // height: MediaQuery.of(context).size.height * .80,
          child: PageView(
            // physics: NeverScrollableScrollPhysics(),
            controller: _pageController,
            children: [
              HomeSlideWidget(pageController: _pageController),
              PhoneAuthentication(pageController: _pageController),
              CodeActivationWidget(pageController: _pageController),
              EnterFullnameWidget(pageController: _pageController),
              AppActivatedWidget(pageController: _pageController),
            ],
          ),
         ),
          // SizedBox(height: 40),
          // Spacer(flex: 1,),
          SvgPicture.asset("assets/images/logo_footer.svg"),
          SizedBox(height: 10)
        ],
      )
    );

}

}
