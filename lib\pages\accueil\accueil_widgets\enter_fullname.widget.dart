
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';

class EnterFullnameWidget extends StatefulWidget{
  const EnterFullnameWidget({super.key, required this.pageController});
  final PageController pageController;

  @override
  State<EnterFullnameWidget> createState() => _EnterFullnameWidgetState();
}

class _EnterFullnameWidgetState extends State<EnterFullnameWidget>{

  final TextEditingController _fullnameController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void dispose(){
    super.dispose();
    _fullnameController.dispose();
  }

  @override
  Widget build(BuildContext context){
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Text("ENTRER VOTRE NOM COMPLET", style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Theme.of(context).primaryColor)),
        SvgPicture.asset("assets/images/logo_kairos.svg"),
        SizedBox(height: 20, 
                 width: 200,
                 child: Divider(color: Theme.of(context).primaryColor, thickness: 5),),
        Spacer(),
        Flexible(flex: 8, child: SvgPicture.asset("assets/images/illustration_profil.svg")),
        // Spacer(flex: 2),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: Text("Veuillez saisir votre nom complet.", textAlign: TextAlign.center),
        ),
        Spacer(flex: 3),
        Form(
          key: _formKey,
          child: SizedBox(
            width: 300,
            child: TextFormField(
              controller: _fullnameController,
              decoration: InputDecoration(
                border: OutlineInputBorder(),
                hintText: "Nom complet",
                hintStyle: TextStyle(color: Colors.grey.shade400),
                contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                ),
              keyboardType: TextInputType.name,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'[^0-9]')),
              ],
              validator: (value){
                if(value!.isEmpty){
                  return "Veuillez saisir votre nom complet";
                } else {
                  return null;
                }
              },
            ),
            
          ),
        ),
        Spacer(),
        FilledButton(
          style: ButtonStyle(
              backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
              fixedSize: WidgetStateProperty.all(Size(300, 50))),
          onPressed: () {
            debugPrint('the user clicked on `Continue` button');
            if(_formKey.currentState!.validate()){
              debugPrint('the user entered: ${_fullnameController.text}');
              widget.pageController.nextPage(duration: Duration(milliseconds: 500), curve: Curves.easeInOut);
            }
          },
          child: Text("TERMINER", style: TextStyle(fontWeight: FontWeight.bold), ),
          ),
        Spacer(flex: 3),
      ],
    );
  }
}