import '../models/user_info.dart';

/// Abstract interface for authentication remote data source
abstract class AuthRemoteDataSource {
  /// Login with email and password
  Future<UserInfoModel> login(String email, String password);
  
  /// Register new user
  Future<void> register(String email, String password, String fullName);
  
  /// Verify PIN code
  Future<void> verifyPin(String pin);
  
  /// Logout current user
  Future<void> logout();
  
  /// Send SMS for verification
  Future<void> sendSms(String phoneNumber);
  
  /// Refresh authentication token
  Future<String> refreshToken();
  
  /// Activate user account
  Future<void> activateAccount(String activationCode);
}

/// Implementation of AuthRemoteDataSource
class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  // TODO: Inject ApiClient
  
  @override
  Future<UserInfoModel> login(String email, String password) async {
    // TODO: Implement API call
    throw UnimplementedError('Login API call not implemented yet');
  }
  
  @override
  Future<void> register(String email, String password, String fullName) async {
    // TODO: Implement API call
    throw UnimplementedError('Register API call not implemented yet');
  }
  
  @override
  Future<void> verifyPin(String pin) async {
    // TODO: Implement API call
    throw UnimplementedError('Verify PIN API call not implemented yet');
  }
  
  @override
  Future<void> logout() async {
    // TODO: Implement API call
    throw UnimplementedError('Logout API call not implemented yet');
  }
  
  @override
  Future<void> sendSms(String phoneNumber) async {
    // TODO: Implement API call
    throw UnimplementedError('Send SMS API call not implemented yet');
  }
  
  @override
  Future<String> refreshToken() async {
    // TODO: Implement API call
    throw UnimplementedError('Refresh token API call not implemented yet');
  }
  
  @override
  Future<void> activateAccount(String activationCode) async {
    // TODO: Implement API call
    throw UnimplementedError('Activate account API call not implemented yet');
  }
}
