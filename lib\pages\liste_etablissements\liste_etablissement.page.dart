import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:Kairos/pages/liste_etablissements/liste_etablissement_widgets/list_schools.widget.dart';
import 'package:Kairos/pages/liste_etablissements/liste_etablissement_widgets/liste_empty_alert.widget.dart';
import 'package:Kairos/pages/liste_etablissements/liste_etablissement_widgets/new_school_dialog.widget.dart';
import 'package:Kairos/widgets/hero_widget.dart';
import 'package:Kairos/widgets/custom_spinner.dart';

class ListeEtablissement extends StatefulWidget{
  const ListeEtablissement({super.key});

  @override
  State<ListeEtablissement> createState() => _ListeEtablissementState();
}

class _ListeEtablissementState extends State<ListeEtablissement>{
  bool _isLoading = true;
  bool isConnected = true; // TODO Check list of schools linked to the account of the user

  @override
  void initState() {
    super.initState();

    // Simulate data loading with a delay
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context){
    return Scaffold(
      body: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          HeroWidget(),
          SizedBox(height: 20),
          Text("LISTE DES ETABLISSEMENTS", style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Theme.of(context).primaryColor)),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20.0),
            child: Text("Veuillez sélectionner votre établissement.", textAlign: TextAlign.center),
          ),
          SizedBox(height: 2),
          _isLoading
          ? Flexible(
              flex: 6,
              child: Center(
                child: CustomSpinner(
                  size: 60.0,
                  strokeWidth: 5.0,
                ),
              ),
            )
          : Flexible(
              flex: 6,
              child: isConnected ? ListSchoolsWidget() : ListeEmptyAlert()
            ),
          SizedBox(height: 20),
          FilledButton(
            style: ButtonStyle(
              backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
              fixedSize: WidgetStateProperty.all(Size(300, 50))
            ),
            onPressed: () {
              debugPrint('the user clicked on `Continue` button');
              showDialog(context: context, builder: (context) => NewSchoolDialogWidget());
            },
            child: Text("ACTIVER NOUVEL ÉTABLISSEMENT", style: TextStyle(fontWeight: FontWeight.bold), ),
          ),
          SizedBox(height: 20),
          SvgPicture.asset("assets/images/logo_footer.svg"),
          SizedBox(height: 10)
        ],
      ),
    );
  }
}