import 'package:flutter/material.dart';

/// Utility method to check if a date is within the specified range.
bool isDateInRange(String itemDate, String startDate, String endDate) {
  try {
    DateTime item = parseDate(itemDate);
    DateTime start = parseDate(startDate);
    DateTime end = parseDate(endDate);

    return item.isAfter(start.subtract(const Duration(days: 1))) &&
           item.isBefore(end.add(const Duration(days: 1)));
  } catch (e) {
    debugPrint("Error parsing dates: $e");
    return true; // If parsing fails, include the item
  }
}

/// Utility method to parse a date string in various formats.
DateTime parseDate(String dateStr) {
  if (dateStr.contains('/')) {
    List<String> parts = dateStr.split('/');
    if (parts.length == 3) {
      // Format: dd/MM/yyyy
      return DateTime(int.parse(parts[2]), int.parse(parts[1]), int.parse(parts[0]));
    } else if (parts.length == 2) {
      // Format: dd/MM - add current year
      int currentYear = DateTime.now().year;
      return DateTime(currentYear, int.parse(parts[1]), int.parse(parts[0]));
    }
  } else if (dateStr.contains('-')) {
    // Format: yyyy-MM-dd
    return DateTime.parse(dateStr);
  }
  throw FormatException("Unable to parse date: $dateStr");
}
