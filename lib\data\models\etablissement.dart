class Etablissement {
  final String apiBaseUrl;
  final String clientId;
  final String clientSecret;
  final String libelleEtab;
  final String codeEtab;
  final String type;
  final String localisation;
  final String urlToken;
  final String logoEtablissement;
  final String? id; // Nullable for creation
  final bool? isActive; // Nullable for creation
  final int? version; // Nullable for creation
  final String? dateCreated; // Nullable for creation
  final String? lastUpdated; // Nullable for creation

  Etablissement({
    required this.apiBaseUrl,
    required this.clientId,
    required this.clientSecret,
    required this.libelleEtab,
    required this.codeEtab,
    required this.type,
    required this.localisation,
    required this.urlToken,
    required this.logoEtablissement,
    this.id,
    this.isActive,
    this.version,
    this.dateCreated,
    this.lastUpdated,
  });

  factory Etablissement.fromJson(Map<String, dynamic> json) {
    return Etablissement(
      apiBaseUrl: json['apiBaseUrl'],
      clientId: json['clientId'],
      clientSecret: json['clientSecret'],
      libelleEtab: json['libelleEtab'],
      codeEtab: json['codeEtab'],
      type: json['type'],
      localisation: json['localisation'],
      urlToken: json['urlToken'],
      logoEtablissement: json['logoEtablissement'],
      id: json['id'],
      isActive: json['isActive'],
      version: json['version'],
      dateCreated: json['dateCreated'],
      lastUpdated: json['lastUpdated'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'apiBaseUrl': apiBaseUrl,
      'clientId': clientId,
      'clientSecret': clientSecret,
      'libelleEtab': libelleEtab,
      'codeEtab': codeEtab,
      'type': type,
      'localisation': localisation,
      'urlToken': urlToken,
      'logoEtablissement': logoEtablissement,
      'id': id,
      'isActive': isActive,
      'version': version,
      'dateCreated': dateCreated,
      'lastUpdated': lastUpdated,
    };
  }
}
