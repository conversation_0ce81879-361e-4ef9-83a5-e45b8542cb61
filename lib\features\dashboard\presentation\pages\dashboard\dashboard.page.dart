import 'package:flutter/material.dart';
import 'package:Kairos/pages/dashboard/dashboard_item.widget.dart';
import 'package:Kairos/widgets/hero_widget.dart';
import 'package:Kairos/constants/dashboard_strings.dart';

class Dashboard extends StatefulWidget {
  const Dashboard({
    super.key,
    this.userName,
  });

  final String? userName;

  @override
  State<Dashboard> createState() => _DashboardState();
}

class _DashboardState extends State<Dashboard> {
  int _currentIndex = 1;

  final dashboardItems = [
    {"title": DashboardStrings.notesTitle, "icon": "icone_notes.svg", "color": Colors.blue},
    {"title": DashboardStrings.absencesTitle, "icon": "icone_absences.svg", "color": Colors.red},
    {"title": DashboardStrings.dossiersTitle, "icon": "icone_dossier_etudiant.svg", "color": Colors.lightBlue},
    {"title": DashboardStrings.financesTitle, "icon": "icone_finance.svg", "color": Colors.amber},
    {"title": DashboardStrings.cahierTitle, "icon": "icone_cahier_de_texte.svg", "color": Colors.orange},
    {"title": DashboardStrings.planningTitle, "icon": "icone_planning_cours.svg", "color": Colors.purple},
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          mainAxisSize: MainAxisSize.min,
          children: [
            HeroWidget(userName: widget.userName),
            SizedBox(height: 20),
            Text(DashboardStrings.dashboardTitle, style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Theme.of(context).primaryColor)),
            Divider(color: Theme.of(context).primaryColor, thickness: 2, height: 20, indent: 100, endIndent: 100,),
            ...dashboardItems.map((item) => DashboardItem(
              title: item["title"]!,
              iconName: item["icon"]!,
            )),
            
          ]),
      ),
        bottomNavigationBar: BottomNavigationBar(
          onTap: (index){
              setState(() => _currentIndex = index);
            if(index == 0){
              debugPrint("user wants to view his profile");
              Navigator.pushNamed(context, "/profile");
            } else if(index == 2){
              debugPrint("user wants to view his notifications");
              Navigator.pushNamed(context, "/notifications");
            } else {
              debugPrint("user wants to view his dashboard");
              Navigator.pushNamed(context, "/dashboard");
            }
          },
          backgroundColor: Colors.black,
          selectedItemColor: Theme.of(context).primaryColor,
          currentIndex: _currentIndex,
          unselectedItemColor: Colors.white,
          items: [
          BottomNavigationBarItem(icon: Icon(Icons.person), label: ""),
          BottomNavigationBarItem(icon: Icon(Icons.dashboard), label: ""),
          BottomNavigationBarItem(icon: Icon(Icons.notifications), label: ""),
        ]),
        );
        
  }
}