
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class AppActivatedWidget extends StatefulWidget{
  const AppActivatedWidget({super.key, required this.pageController});
  final PageController pageController;

  @override
  State<AppActivatedWidget> createState() => _AppActivatedWidgetState();
}

class _AppActivatedWidgetState extends State<AppActivatedWidget>{

  final TextEditingController _fullnameController = TextEditingController();

  @override
  void dispose(){
    super.dispose();
    _fullnameController.dispose();
  }

  @override
  Widget build(BuildContext context){
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Text("FÉLICITATIONS!", style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Theme.of(context).primaryColor)),
        SvgPicture.asset("assets/images/logo_kairos.svg"),
        SizedBox(height: 20, 
                 width: 200,
                 child: Divider(color: Theme.of(context).primaryColor, thickness: 5),),
        Spacer(),
        SvgPicture.asset("assets/images/illustration_felicitation.svg"),
        Spacer(flex: 2),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: Text("Votre appliction KAIROS mobile a été activée avec succès.", textAlign: TextAlign.center),
        ),
        SizedBox(height: 20),
       
        FilledButton(
          style: ButtonStyle(
              backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
              fixedSize: WidgetStateProperty.all(Size(300, 50))),
          onPressed: () {
            debugPrint('the user clicked on `Continue` button');
              Navigator.pushReplacementNamed(context, "/liste_etablissement");
            },
          child: Text("ACCÉDER Á MON ESPACE", style: TextStyle(fontWeight: FontWeight.bold), ),
          ),
          SizedBox(height: 20),
        Spacer(),
      ],
    );
  }
}