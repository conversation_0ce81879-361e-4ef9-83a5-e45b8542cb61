// Base class for user information, used in various requests
class UserInfo {
  final String numeroTelephone;
  final String marqueTelephone;
  final String modelTelephone;
  final String imeiTelephone;
  final String numeroSerie;

  UserInfo({
    required this.numeroTelephone,
    required this.marqueTelephone,
    required this.modelTelephone,
    required this.imeiTelephone,
    required this.numeroSerie,
  });

  factory UserInfo.fromJson(Map<String, dynamic> json) {
    return UserInfo(
      numeroTelephone: json['numeroTelephone'],
      marqueTelephone: json['marqueTelephone'],
      modelTelephone: json['modelTelephone'],
      imeiTelephone: json['imeiTelephone'],
      numeroSerie: json['numeroSerie'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'numeroTelephone': numeroTelephone,
      'marqueTelephone': marqueTelephone,
      'modelTelephone': modelTelephone,
      'imeiTelephone': imeiTelephone,
      'numeroSerie': numeroSerie,
    };
  }
}
