import 'package:flutter/material.dart';
import 'package:Kairos/widgets/custom_app_bar.widget.dart';
import 'package:Kairos/widgets/search_bar_sliver.widget.dart';
import 'package:Kairos/data/enums/header_enums.dart';
import 'package:Kairos/data/models/student_record.dart';

class DossierSelectionPage extends StatefulWidget {
  const DossierSelectionPage({super.key});

  @override
  State<DossierSelectionPage> createState() => _DossierSelectionPageState();
}

class _DossierSelectionPageState extends State<DossierSelectionPage> with TickerProviderStateMixin {
  bool _isSearchBarVisible = false;
  final TextEditingController _searchController = TextEditingController();
  late List<StudentRecord> filteredStudents = [];
  late AnimationController _searchAnimationController;

  final List<StudentRecord> studentsData = [
    StudentRecord(
      id: '218553',
      name: '<PERSON><PERSON><PERSON><PERSON> SARR',
      class_: 'PALT_C1B_2021',
      birthdate: '02/03/2000',
    ),
    StudentRecord(
      id: '218554',
      name: '<PERSON>',
      class_: 'PALT_C1B_2021',
      birthdate: '10/06/1999',
    ),
  ];

  @override
  void initState() {
    super.initState();
    filteredStudents = List.from(studentsData);
    _searchController.addListener(_filterStudents);
    _searchAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 100),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchAnimationController.dispose();
    super.dispose();
  }

  void _filterStudents() {
    final String query = _searchController.text.toLowerCase();
    setState(() {
      filteredStudents = studentsData.where((student) {
        return student.name.toLowerCase().contains(query) ||
               student.id.toLowerCase().contains(query) ||
               student.class_.toLowerCase().contains(query);
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          CustomAppBar(
            pageSection: HeaderEnum.dossiers,
            title: "COMPTES ÉTUDIANTS",
            isSearchBarVisible: _isSearchBarVisible,
            onSearchTap: () {
              setState(() {
                _isSearchBarVisible = !_isSearchBarVisible;
                if (!_isSearchBarVisible) {
                  _searchAnimationController.reverse();
                  _searchController.clear();
                } else {
                  _searchAnimationController.forward();
                }
              });
            },
          ),

          SliverToBoxAdapter(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                  child: Text(
                    "À quel dossier souhaitez-vous accéder ?".toUpperCase(),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Divider(color: Theme.of(context).primaryColor, thickness: 2, height: 20, indent: 100, endIndent: 100,),
              ],
            ),
          ),
          AnimatedBuilder(
            animation: _searchAnimationController,
            builder: (context, child) {
              return SliverPersistentHeader(
                delegate: SearchBarSliver(
                  extentHeight: _searchAnimationController.value * 60.0,
                  searchController: _searchController,
                  onSearchChanged: (query) => _filterStudents(),
                  hintText: "Rechercher un dossier...",
                  showDateFilter: false,
                ),
              );
            },
          ),
          SliverPadding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            sliver: SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final student = filteredStudents[index];
                  return Card(
                    color: Colors.white,
                    margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0), // Add margin to cards
                    child: ListTile(
                      leading: CircleAvatar(
                        radius: 20,
                        backgroundColor: Colors.grey[300],
                        child: const Icon(Icons.person, color: Colors.grey),
                      ),
                      title: Text(
                        '${student.id} - ${student.name}',
                        style: TextStyle(
                          color: Theme.of(context).primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      subtitle: Text(student.class_),
                      onTap: () {
                        Navigator.pushNamed(
                          context,
                          '/dashboard',
                          arguments: student.name,
                        );
                      },
                    ),
                  );
                },
                childCount: filteredStudents.length,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
