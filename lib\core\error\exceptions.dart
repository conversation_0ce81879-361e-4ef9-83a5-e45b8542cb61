/// Base exception class for the application
abstract class AppException implements Exception {
  final String message;
  final String? code;
  
  const AppException(this.message, [this.code]);
  
  @override
  String toString() => 'AppException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Server-related exceptions
class ServerException extends AppException {
  const ServerException(super.message, [super.code]);
}

/// Network-related exceptions
class NetworkException extends AppException {
  const NetworkException(super.message, [super.code]);
}

/// Cache-related exceptions
class CacheException extends AppException {
  const CacheException(super.message, [super.code]);
}

/// Validation-related exceptions
class ValidationException extends AppException {
  const ValidationException(super.message, [super.code]);
}

/// Authentication-related exceptions
class AuthenticationException extends AppException {
  const AuthenticationException(super.message, [super.code]);
}

/// Authorization-related exceptions
class AuthorizationException extends AppException {
  const AuthorizationException(super.message, [super.code]);
}

/// Format-related exceptions
class FormatException extends AppException {
  const FormatException(super.message, [super.code]);
}

/// Timeout-related exceptions
class TimeoutException extends AppException {
  const TimeoutException(super.message, [super.code]);
}
