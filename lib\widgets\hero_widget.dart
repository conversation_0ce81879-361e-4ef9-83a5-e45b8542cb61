import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:Kairos/data/enums/header_enums.dart';

class HeroWidget extends StatelessWidget {
  const HeroWidget({
    super.key, 
    this.pageSection = HeaderEnum.dashboard,
    this.userName,
  });
  
  final HeaderEnum pageSection;
  final String? userName;

  @override
  Widget build(BuildContext context) {
    return Hero(
      tag: "hero_dashboard",
      transitionOnUserGestures: true,
      child: Stack(
        children: [
          Image.asset(
            pageSection == HeaderEnum.dashboard ? "assets/images/header_dashboard.png" :
            pageSection == HeaderEnum.notes ? "assets/images/header_notes.png" :
            pageSection == HeaderEnum.absences ? "assets/images/header_absences.png" :
            pageSection == HeaderEnum.cahierDeTexte ? "assets/images/header_cahier_texte.png" :
            pageSection == HeaderEnum.planning ? "assets/images/header_planning.png" :
            pageSection == HeaderEnum.dossiers ? "assets/images/header_dossiers.png" :
            pageSection == HeaderEnum.finances ? "assets/images/header_finances.png" :
            "assets/images/header_dashboard.png",
            height: 200,
            width: MediaQuery.of(context).size.width,
            fit: BoxFit.cover,
          ),
          Positioned(
            top: 60,
            left: 20,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SvgPicture.asset(
                  pageSection == HeaderEnum.dashboard
                      ? "assets/images/logo_kairos.svg"
                      : "assets/images/logo_kairos_blanc.svg"
                ),
                Text(
                  userName ?? "Papa Amadou",
                  textAlign: TextAlign.left,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                CircleAvatar(
                  radius: 20,
                  backgroundImage: Image.asset("assets/images/img_user.png").image,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}