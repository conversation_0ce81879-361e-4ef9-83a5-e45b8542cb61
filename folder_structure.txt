# Flutter BLoC Architecture Structure (Feature-First with Clean Architecture)
# This structure implements BLoC pattern with Cubits for state management and follows clean architecture principles.
# IMPLEMENTATION STATUS: ✅ IMPLEMENTED

/lib
|
|-- main.dart                   # App entry point with BlocProviders setup and app initialization.
|
|-- core/                       # Shared code used across multiple features.
|   |
|   |-- api/
|   |   |-- api_client.dart       # Central API client (Dio setup, interceptors, base configuration).
|   |   |-- api_endpoints.dart    # All API endpoint constants.
|   |   `-- api_exceptions.dart   # Custom API exception classes.
|   |
|   |-- constants/
|   |   |-- app_colors.dart       # App-wide color palette.
|   |   |-- app_constants.dart    # Global constants (durations, keys, sizes).
|   |   |-- app_strings.dart      # Localization strings and text constants.
|   |   `-- app_assets.dart       # Asset paths constants.
|   |
|   |-- di/                      # Dependency Injection
|   |   `-- injection_container.dart # Service locator setup (GetIt, Injectable).
|   |
|   |-- error/
|   |   |-- failures.dart         # Abstract failure classes.
|   |   `-- exceptions.dart       # Custom exception classes.
|   |
|   |-- navigation/
|   |   |-- app_router.dart       # Routing logic (GoRouter, AutoRoute).
|   |   `-- route_names.dart      # Route name constants.
|   |
|   |-- network/
|   |   |-- network_info.dart     # Network connectivity checker.
|   |   `-- network_constants.dart # Network-related constants.
|   |
|   |-- services/
|   |   |-- storage_service.dart  # Local storage abstraction (SharedPreferences, Secure Storage).
|   |   |-- device_info_service.dart # Device information service.
|   |   |-- notification_service.dart # Push notification service.
|   |   `-- cache_service.dart    # Caching service for offline support.
|   |
|   |-- theme/
|   |   |-- app_theme.dart        # App's ThemeData configuration.
|   |   |-- color_schemes.dart    # Light and dark color schemes.
|   |   `-- text_styles.dart      # Text style definitions.
|   |
|   |-- utils/
|   |   |-- formatters.dart       # Date, currency, and other formatters.
|   |   |-- validators.dart       # Form field validators.
|   |   |-- extensions.dart       # Dart extensions for common operations.
|   |   |-- helpers.dart          # Helper functions and utilities.
|   |   `-- date_utils.dart       # Date manipulation utilities.
|   |
|   `-- widgets/                  # Generic, reusable widgets shared across features.
|       |-- buttons/
|       |   |-- custom_button.dart
|       |   |-- icon_button.dart
|       |   `-- floating_action_button.dart
|       |-- indicators/
|       |   |-- loading_indicator.dart
|       |   |-- progress_indicator.dart
|       |   `-- custom_spinner.dart
|       |-- dialogs/
|       |   |-- error_dialog.dart
|       |   |-- confirmation_dialog.dart
|       |   `-- info_dialog.dart
|       |-- inputs/
|       |   |-- custom_text_field.dart
|       |   |-- search_bar.dart
|       |   `-- date_picker.dart
|       |-- layout/
|       |   |-- custom_app_bar.dart
|       |   |-- custom_scaffold.dart
|       |   `-- responsive_layout.dart
|       `-- common/
|           |-- empty_state.dart
|           |-- error_widget.dart
|           |-- hero_widget.dart
|           `-- snackbar_widget.dart
|
`-- features/                   # Contains all the individual features of the app.
    |
    |-- authentication/           # Authentication feature (Login, Register, PIN verification).
    |   |
    |   |-- data/                 # --- Data Layer ---
    |   |   |
    |   |   |-- datasources/
    |   |   |   |-- auth_local_datasource.dart    # Local storage for auth data.
    |   |   |   `-- auth_remote_datasource.dart   # API calls for authentication.
    |   |   |
    |   |   |-- models/
    |   |   |   |-- user_model.dart               # User data model with fromJson/toJson.
    |   |   |   |-- login_request_model.dart      # Login request model.
    |   |   |   |-- login_response_model.dart     # Login response model.
    |   |   |   |-- activation_request_model.dart # Account activation model.
    |   |   |   `-- verification_pin_model.dart   # PIN verification model.
    |   |   |
    |   |   `-- repositories/
    |   |       `-- auth_repository_impl.dart     # Implementation of auth repository.
    |   |
    |   |-- domain/               # --- Domain Layer ---
    |   |   |
    |   |   |-- entities/
    |   |   |   |-- user.dart                     # User entity (business logic model).
    |   |   |   `-- auth_status.dart              # Authentication status entity.
    |   |   |
    |   |   |-- repositories/
    |   |   |   `-- auth_repository.dart          # Abstract auth repository interface.
    |   |   |
    |   |   `-- usecases/
    |   |       |-- login_usecase.dart            # Login business logic.
    |   |       |-- logout_usecase.dart           # Logout business logic.
    |   |       |-- register_usecase.dart         # Registration business logic.
    |   |       |-- verify_pin_usecase.dart       # PIN verification business logic.
    |   |       `-- check_auth_status_usecase.dart # Check authentication status.
    |   |
    |   `-- presentation/         # --- Presentation Layer ---
    |       |
    |       |-- bloc/
    |       |   |-- auth_cubit.dart               # Authentication state management.
    |       |   `-- auth_state.dart               # Authentication states.
    |       |
    |       |-- pages/
    |       |   |-- welcome_page.dart             # Welcome/onboarding page.
    |       |   |-- login_page.dart               # Login page.
    |       |   |-- registration_page.dart        # Registration page.
    |       |   |-- pin_verification_page.dart    # PIN verification page.
    |       |   `-- account_activation_page.dart  # Account activation page.
    |       |
    |       `-- widgets/
    |           |-- login_form.dart               # Login form widget.
    |           |-- registration_form.dart        # Registration form widget.
    |           |-- pin_input_widget.dart         # PIN input widget.
    |           `-- auth_button.dart              # Authentication button widget.
    |
    |-- dashboard/                # Dashboard feature (Main navigation hub).
    |   |
    |   |-- data/
    |   |   |-- datasources/
    |   |   |   `-- dashboard_remote_datasource.dart
    |   |   |-- models/
    |   |   |   `-- dashboard_item_model.dart
    |   |   `-- repositories/
    |   |       `-- dashboard_repository_impl.dart
    |   |
    |   |-- domain/
    |   |   |-- entities/
    |   |   |   `-- dashboard_item.dart
    |   |   |-- repositories/
    |   |   |   `-- dashboard_repository.dart
    |   |   `-- usecases/
    |   |       `-- get_dashboard_items_usecase.dart
    |   |
    |   `-- presentation/
    |       |-- bloc/
    |       |   |-- dashboard_cubit.dart
    |       |   `-- dashboard_state.dart
    |       |-- pages/
    |       |   `-- dashboard_page.dart
    |       `-- widgets/
    |           |-- dashboard_item_widget.dart
    |           |-- dashboard_grid.dart
    |           `-- user_info_header.dart
    |
    |-- finances/                 # Financial management feature.
    |   |
    |   |-- data/
    |   |   |-- datasources/
    |   |   |   |-- finance_local_datasource.dart
    |   |   |   `-- finance_remote_datasource.dart
    |   |   |-- models/
    |   |   |   |-- finance_fee_model.dart        # Fee data model.
    |   |   |   |-- payment_model.dart            # Payment data model.
    |   |   |   `-- financial_summary_model.dart  # Financial summary model.
    |   |   `-- repositories/
    |   |       `-- finance_repository_impl.dart
    |   |
    |   |-- domain/
    |   |   |-- entities/
    |   |   |   |-- finance_fee.dart              # Fee entity.
    |   |   |   |-- payment.dart                  # Payment entity.
    |   |   |   `-- financial_summary.dart        # Financial summary entity.
    |   |   |-- repositories/
    |   |   |   `-- finance_repository.dart
    |   |   `-- usecases/
    |   |       |-- get_paid_fees_usecase.dart
    |   |       |-- get_unpaid_fees_usecase.dart
    |   |       |-- get_financial_summary_usecase.dart
    |   |       `-- search_fees_usecase.dart
    |   |
    |   `-- presentation/
    |       |-- bloc/
    |       |   |-- finance_cubit.dart            # Finance state management.
    |       |   `-- finance_state.dart            # Finance states (loading, loaded, error).
    |       |-- pages/
    |       |   `-- finances_page.dart            # Main finances page with tabs.
    |       `-- widgets/
    |           |-- finance_item_widget.dart      # Individual fee item widget.
    |           |-- fees_tab_view.dart            # Tab view for paid/unpaid fees.
    |           |-- financial_summary_card.dart   # Summary card widget.
    |           `-- fee_filter_widget.dart        # Filter widget for fees.
    |
    |-- grades/                   # Grades and evaluations feature.
    |   |
    |   |-- data/
    |   |   |-- datasources/
    |   |   |   |-- grades_local_datasource.dart
    |   |   |   `-- grades_remote_datasource.dart
    |   |   |-- models/
    |   |   |   |-- grade_model.dart              # Grade data model.
    |   |   |   |-- evaluation_model.dart         # Evaluation data model.
    |   |   |   `-- subject_model.dart            # Subject data model.
    |   |   `-- repositories/
    |   |       `-- grades_repository_impl.dart
    |   |
    |   |-- domain/
    |   |   |-- entities/
    |   |   |   |-- grade.dart                    # Grade entity.
    |   |   |   |-- evaluation.dart               # Evaluation entity.
    |   |   |   `-- subject.dart                  # Subject entity.
    |   |   |-- repositories/
    |   |   |   `-- grades_repository.dart
    |   |   `-- usecases/
    |   |       |-- get_grades_usecase.dart
    |   |       |-- get_evaluations_usecase.dart
    |   |       |-- search_grades_usecase.dart
    |   |       `-- filter_grades_by_date_usecase.dart
    |   |
    |   `-- presentation/
    |       |-- bloc/
    |       |   |-- grades_cubit.dart             # Grades state management.
    |       |   `-- grades_state.dart             # Grades states.
    |       |-- pages/
    |       |   `-- grades_page.dart              # Main grades page (formerly notes_page).
    |       `-- widgets/
    |           |-- grade_item_widget.dart        # Individual grade item.
    |           |-- grade_statistics_widget.dart  # Grade statistics display.
    |           |-- subject_filter_widget.dart    # Subject filter widget.
    |           `-- grade_chart_widget.dart       # Grade visualization chart.
    |
    |-- schedule/                 # Schedule/timetable feature.
    |   |
    |   |-- data/
    |   |   |-- datasources/
    |   |   |   |-- schedule_local_datasource.dart
    |   |   |   `-- schedule_remote_datasource.dart
    |   |   |-- models/
    |   |   |   |-- schedule_entry_model.dart     # Schedule entry data model.
    |   |   |   |-- course_model.dart             # Course data model.
    |   |   |   `-- teacher_model.dart            # Teacher data model.
    |   |   `-- repositories/
    |   |       `-- schedule_repository_impl.dart
    |   |
    |   |-- domain/
    |   |   |-- entities/
    |   |   |   |-- schedule_entry.dart           # Schedule entry entity.
    |   |   |   |-- course.dart                   # Course entity.
    |   |   |   `-- teacher.dart                  # Teacher entity.
    |   |   |-- repositories/
    |   |   |   `-- schedule_repository.dart
    |   |   `-- usecases/
    |   |       |-- get_weekly_schedule_usecase.dart
    |   |       |-- get_daily_schedule_usecase.dart
    |   |       |-- search_schedule_usecase.dart
    |   |       `-- filter_schedule_by_date_usecase.dart
    |   |
    |   `-- presentation/
    |       |-- bloc/
    |       |   |-- schedule_cubit.dart           # Schedule state management.
    |       |   `-- schedule_state.dart           # Schedule states.
    |       |-- pages/
    |       |   `-- schedule_page.dart            # Main schedule page (formerly emploi_du_temps).
    |       `-- widgets/
    |           |-- schedule_item_widget.dart     # Individual schedule item.
    |           |-- daily_schedule_widget.dart    # Daily schedule view.
    |           |-- weekly_schedule_widget.dart   # Weekly schedule view.
    |           `-- schedule_filter_widget.dart   # Schedule filter widget.
    |
    |-- absences/                 # Absences and tardiness tracking feature.
    |   |
    |   |-- data/
    |   |   |-- datasources/
    |   |   |   |-- absences_local_datasource.dart
    |   |   |   `-- absences_remote_datasource.dart
    |   |   |-- models/
    |   |   |   |-- absence_model.dart            # Absence data model.
    |   |   |   |-- tardiness_model.dart          # Tardiness data model.
    |   |   |   `-- attendance_summary_model.dart # Attendance summary model.
    |   |   `-- repositories/
    |   |       `-- absences_repository_impl.dart
    |   |
    |   |-- domain/
    |   |   |-- entities/
    |   |   |   |-- absence.dart                  # Absence entity.
    |   |   |   |-- tardiness.dart                # Tardiness entity.
    |   |   |   `-- attendance_summary.dart       # Attendance summary entity.
    |   |   |-- repositories/
    |   |   |   `-- absences_repository.dart
    |   |   `-- usecases/
    |   |       |-- get_absences_usecase.dart
    |   |       |-- get_tardiness_usecase.dart
    |   |       |-- get_attendance_summary_usecase.dart
    |   |       `-- search_absences_usecase.dart
    |   |
    |   `-- presentation/
    |       |-- bloc/
    |       |   |-- absences_cubit.dart           # Absences state management.
    |       |   `-- absences_state.dart           # Absences states.
    |       |-- pages/
    |       |   `-- absences_page.dart            # Main absences page (formerly absences_retards).
    |       `-- widgets/
    |           |-- absence_item_widget.dart      # Individual absence item.
    |           |-- tardiness_item_widget.dart    # Individual tardiness item.
    |           |-- attendance_summary_widget.dart # Attendance summary display.
    |           `-- absence_filter_widget.dart    # Absence filter widget.
    |
    |-- course_log/               # Course log/textbook feature (cahier de texte).
    |   |
    |   |-- data/
    |   |   |-- datasources/
    |   |   |   |-- course_log_local_datasource.dart
    |   |   |   `-- course_log_remote_datasource.dart
    |   |   |-- models/
    |   |   |   |-- course_log_model.dart         # Course log data model.
    |   |   |   |-- lesson_model.dart             # Lesson data model.
    |   |   |   `-- homework_model.dart           # Homework data model.
    |   |   `-- repositories/
    |   |       `-- course_log_repository_impl.dart
    |   |
    |   |-- domain/
    |   |   |-- entities/
    |   |   |   |-- course_log.dart               # Course log entity.
    |   |   |   |-- lesson.dart                   # Lesson entity.
    |   |   |   `-- homework.dart                 # Homework entity.
    |   |   |-- repositories/
    |   |   |   `-- course_log_repository.dart
    |   |   `-- usecases/
    |   |       |-- get_course_logs_usecase.dart
    |   |       |-- get_lessons_usecase.dart
    |   |       |-- get_homework_usecase.dart
    |   |       `-- search_course_logs_usecase.dart
    |   |
    |   `-- presentation/
    |       |-- bloc/
    |       |   |-- course_log_cubit.dart         # Course log state management.
    |       |   `-- course_log_state.dart         # Course log states.
    |       |-- pages/
    |       |   `-- course_log_page.dart          # Main course log page (formerly cahier_texte).
    |       `-- widgets/
    |           |-- course_log_item_widget.dart   # Individual course log item.
    |           |-- lesson_widget.dart            # Lesson display widget.
    |           |-- homework_widget.dart          # Homework display widget.
    |           `-- course_log_filter_widget.dart # Course log filter widget.
    |
    |-- student_records/          # Student records/dossiers feature.
    |   |
    |   |-- data/
    |   |   |-- datasources/
    |   |   |   |-- student_records_local_datasource.dart
    |   |   |   `-- student_records_remote_datasource.dart
    |   |   |-- models/
    |   |   |   |-- student_record_model.dart     # Student record data model.
    |   |   |   |-- document_model.dart           # Document data model.
    |   |   |   `-- academic_info_model.dart      # Academic information model.
    |   |   `-- repositories/
    |   |       `-- student_records_repository_impl.dart
    |   |
    |   |-- domain/
    |   |   |-- entities/
    |   |   |   |-- student_record.dart           # Student record entity.
    |   |   |   |-- document.dart                 # Document entity.
    |   |   |   `-- academic_info.dart            # Academic information entity.
    |   |   |-- repositories/
    |   |   |   `-- student_records_repository.dart
    |   |   `-- usecases/
    |   |       |-- get_student_records_usecase.dart
    |   |       |-- get_documents_usecase.dart
    |   |       |-- get_academic_info_usecase.dart
    |   |       `-- search_records_usecase.dart
    |   |
    |   `-- presentation/
    |       |-- bloc/
    |       |   |-- student_records_cubit.dart    # Student records state management.
    |       |   `-- student_records_state.dart    # Student records states.
    |       |-- pages/
    |       |   `-- student_records_page.dart     # Main student records page (formerly dossiers).
    |       `-- widgets/
    |           |-- student_record_item_widget.dart # Individual record item.
    |           |-- document_list_widget.dart     # Document list display.
    |           |-- academic_info_widget.dart     # Academic information display.
    |           `-- record_filter_widget.dart     # Record filter widget.
    |
    |-- profile/                  # User profile management feature.
    |   |
    |   |-- data/
    |   |   |-- datasources/
    |   |   |   |-- profile_local_datasource.dart
    |   |   |   `-- profile_remote_datasource.dart
    |   |   |-- models/
    |   |   |   |-- user_profile_model.dart       # User profile data model.
    |   |   |   |-- school_model.dart             # School data model.
    |   |   |   `-- profile_settings_model.dart   # Profile settings model.
    |   |   `-- repositories/
    |   |       `-- profile_repository_impl.dart
    |   |
    |   |-- domain/
    |   |   |-- entities/
    |   |   |   |-- user_profile.dart             # User profile entity.
    |   |   |   |-- school.dart                   # School entity.
    |   |   |   `-- profile_settings.dart         # Profile settings entity.
    |   |   |-- repositories/
    |   |   |   `-- profile_repository.dart
    |   |   `-- usecases/
    |   |       |-- get_user_profile_usecase.dart
    |   |       |-- update_profile_usecase.dart
    |   |       |-- get_user_schools_usecase.dart
    |   |       `-- update_profile_settings_usecase.dart
    |   |
    |   `-- presentation/
    |       |-- bloc/
    |       |   |-- profile_cubit.dart            # Profile state management.
    |       |   `-- profile_state.dart            # Profile states.
    |       |-- pages/
    |       |   |-- profile_page.dart             # Main profile page.
    |       |   `-- profile_settings_page.dart    # Profile settings page.
    |       `-- widgets/
    |           |-- profile_info_widget.dart      # Profile information display.
    |           |-- profile_avatar_widget.dart    # Profile avatar widget.
    |           |-- school_selection_widget.dart  # School selection widget.
    |           `-- profile_settings_widget.dart  # Profile settings widget.
    |
    |-- notifications/            # Notifications management feature.
    |   |
    |   |-- data/
    |   |   |-- datasources/
    |   |   |   |-- notifications_local_datasource.dart
    |   |   |   `-- notifications_remote_datasource.dart
    |   |   |-- models/
    |   |   |   |-- notification_model.dart       # Notification data model.
    |   |   |   `-- notification_settings_model.dart # Notification settings model.
    |   |   `-- repositories/
    |   |       `-- notifications_repository_impl.dart
    |   |
    |   |-- domain/
    |   |   |-- entities/
    |   |   |   |-- notification.dart             # Notification entity.
    |   |   |   `-- notification_settings.dart    # Notification settings entity.
    |   |   |-- repositories/
    |   |   |   `-- notifications_repository.dart
    |   |   `-- usecases/
    |   |       |-- get_notifications_usecase.dart
    |   |       |-- mark_notification_read_usecase.dart
    |   |       |-- delete_notification_usecase.dart
    |   |       `-- update_notification_settings_usecase.dart
    |   |
    |   `-- presentation/
    |       |-- bloc/
    |       |   |-- notifications_cubit.dart      # Notifications state management.
    |       |   `-- notifications_state.dart      # Notifications states.
    |       |-- pages/
    |       |   |-- notifications_page.dart       # Main notifications page.
    |       |   `-- notification_settings_page.dart # Notification settings page.
    |       `-- widgets/
    |           |-- notification_item_widget.dart # Individual notification item.
    |           |-- notification_list_widget.dart # Notification list display.
    |           `-- notification_settings_widget.dart # Notification settings widget.
    |
    |-- schools/                  # Schools/establishments management feature.
    |   |
    |   |-- data/
    |   |   |-- datasources/
    |   |   |   |-- schools_local_datasource.dart
    |   |   |   `-- schools_remote_datasource.dart
    |   |   |-- models/
    |   |   |   |-- school_model.dart             # School data model.
    |   |   |   `-- school_selection_model.dart   # School selection model.
    |   |   `-- repositories/
    |   |       `-- schools_repository_impl.dart
    |   |
    |   |-- domain/
    |   |   |-- entities/
    |   |   |   |-- school.dart                   # School entity.
    |   |   |   `-- school_selection.dart         # School selection entity.
    |   |   |-- repositories/
    |   |   |   `-- schools_repository.dart
    |   |   `-- usecases/
    |   |       |-- get_available_schools_usecase.dart
    |   |       |-- get_user_schools_usecase.dart
    |   |       |-- select_school_usecase.dart
    |   |       `-- search_schools_usecase.dart
    |   |
    |   `-- presentation/
    |       |-- bloc/
    |       |   |-- schools_cubit.dart            # Schools state management.
    |       |   `-- schools_state.dart            # Schools states.
    |       |-- pages/
    |       |   |-- schools_list_page.dart        # Schools list page (formerly liste_etablissements).
    |       |   `-- school_selection_page.dart    # School selection page (formerly dossier_selection).
    |       `-- widgets/
    |           |-- school_item_widget.dart       # Individual school item.
    |           |-- school_list_widget.dart       # School list display.
    |           `-- school_search_widget.dart     # School search widget.
    |
    `-- splash/                   # Splash screen and app initialization feature.
        |
        |-- data/
        |   |-- datasources/
        |   |   `-- app_initialization_datasource.dart
        |   |-- models/
        |   |   `-- app_config_model.dart         # App configuration model.
        |   `-- repositories/
        |       `-- app_initialization_repository_impl.dart
        |
        |-- domain/
        |   |-- entities/
        |   |   `-- app_config.dart               # App configuration entity.
        |   |-- repositories/
        |   |   `-- app_initialization_repository.dart
        |   `-- usecases/
        |       |-- initialize_app_usecase.dart
        |       `-- check_authentication_usecase.dart
        |
        `-- presentation/
            |-- bloc/
            |   |-- splash_cubit.dart             # Splash state management.
            |   `-- splash_state.dart             # Splash states.
            |-- pages/
            |   `-- splash_page.dart              # Splash screen page.
            `-- widgets/
                |-- splash_logo_widget.dart       # Splash logo animation.
                `-- loading_animation_widget.dart # Loading animation widget.


# BLoC Architecture Implementation Notes:

## Key Principles Applied:
1. **Clean Architecture**: Separation of concerns with Data, Domain, and Presentation layers.
2. **Feature-First Organization**: Each feature is self-contained with its own layers.
3. **Dependency Inversion**: Domain layer doesn't depend on external frameworks.
4. **Single Responsibility**: Each class has a single, well-defined purpose.
5. **Testability**: Clear separation makes unit testing straightforward.

## State Management with Cubits:
- Each feature has its own Cubit for state management
- States are clearly defined (Initial, Loading, Loaded, Error)
- Business logic is separated from UI logic
- Reactive programming with streams for real-time updates

## Data Layer Architecture:
- **DataSources**: Handle data retrieval (local/remote)
- **Models**: Data transfer objects with JSON serialization
- **Repositories**: Implement domain repository interfaces

## Domain Layer Architecture:
- **Entities**: Pure business objects without external dependencies
- **Repositories**: Abstract interfaces for data access
- **UseCases**: Encapsulate business logic and rules

## Presentation Layer Architecture:
- **Bloc/Cubit**: State management and business logic coordination
- **Pages**: Screen-level widgets that consume state
- **Widgets**: Reusable UI components specific to features

## Migration Strategy:
1. Start with core infrastructure (DI, API client, base classes)
2. Implement one feature at a time (recommended: Authentication first)
3. Gradually migrate existing StatefulWidgets to BlocConsumer/BlocBuilder
4. Add repositories and use cases incrementally
5. Implement offline support and caching as needed

## Dependencies to Add:
- flutter_bloc: ^8.1.3
- equatable: ^2.0.5
- get_it: ^7.6.4
- injectable: ^2.3.2
- dartz: ^0.10.1 (for Either type)
- connectivity_plus: ^5.0.1 (for network checking)

