



import 'package:flutter/material.dart';
import 'package:Kairos/data/models/dossier.dart';

class DossierDetailsDialogWidget extends StatelessWidget {
  const DossierDetailsDialogWidget({super.key, required this.dossier});
  final Dossier dossier;


  @override
  Widget build(BuildContext context){
    return Dialog(
      insetPadding: EdgeInsets.symmetric(horizontal: 10),
      backgroundColor: Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              children: [
                  ListTile(
                          leading: CircleAvatar(radius: 30, 
                                                backgroundColor: Theme.of(context).primaryColor, 
                                                child: Text(dossier.initials, style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),)),
                          title: Text(dossier.title, style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(dossier.boldTitle),
                              Text(dossier.description, overflow: TextOverflow.ellipsis, maxLines: 1, style: TextStyle(color: Colors.grey)),
                            ],
                          ),
                    ),  
                     Positioned(
                    top: 0,
                    right: 0, 
                    child: 
                    Container(
                      decoration: BoxDecoration(color: Theme.of(context).colorScheme.secondary, borderRadius: BorderRadius.circular(100)),
                      child:InkWell(
                    borderRadius: BorderRadius.circular(14),
                    onTap: () => Navigator.of(context).pop(),
                    child: const Padding(
                      padding: EdgeInsets.all(8.0),
                      child: Icon(Icons.close, color: Colors.white, size: 20),
                    ),
                  ),
                                        )
                ),
              ],),
                // SizedBox(height: 20),
                Divider(color: Theme.of(context).primaryColor, thickness: 1, ),
                Text.rich(
                  TextSpan(children: [
                    TextSpan(text: "Objet: ", style: TextStyle(fontWeight: FontWeight.bold)),
                    TextSpan(text: "Entretien Individuel"),
                  ])
                ),
                SizedBox(height: 3),
              
                Flexible(child: Text("${dossier.description} ... lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum."),
                ),
                SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                     FilledButton(
                      onPressed: (){},
                      style: ButtonStyle(
                        minimumSize: WidgetStateProperty.all(Size(150, 50)),
                        backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.download),
                          Text("Ouvrir la piéce jointe"),
                        ],
                      ),
                    ),
                  ],
                )
            
          ],
        ),
      ),
    );
  }
}