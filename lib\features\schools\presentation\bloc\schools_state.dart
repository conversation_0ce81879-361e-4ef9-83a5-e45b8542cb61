﻿import 'package:equatable/equatable.dart';

/// Base schools state
abstract class SchoolsState extends Equatable {
  const SchoolsState();
  
  @override
  List<Object?> get props => [];
}

/// Initial schools state
class SchoolsInitial extends SchoolsState {
  const SchoolsInitial();
}

/// Loading state during schools operations
class SchoolsLoading extends SchoolsState {
  const SchoolsLoading();
}

/// Schools data loaded successfully
class SchoolsLoaded extends SchoolsState {
  final List<dynamic> data; // TODO: Replace with proper type
  
  const SchoolsLoaded({required this.data});
  
  @override
  List<Object?> get props => [data];
}

/// Schools error occurred
class SchoolsError extends SchoolsState {
  final String message;
  
  const SchoolsError(this.message);
  
  @override
  List<Object?> get props => [message];
}
