# PowerShell script to create BLoC feature structure
$features = @(
    "dashboard", "finances", "grades", "schedule", "absences", 
    "course_log", "student_records", "profile", "notifications", "schools", "splash"
)

foreach ($feature in $features) {
    $basePath = "lib/features/$feature"
    
    # Create data layer directories
    New-Item -ItemType Directory -Path "$basePath/data/datasources" -Force | Out-Null
    New-Item -ItemType Directory -Path "$basePath/data/models" -Force | Out-Null
    New-Item -ItemType Directory -Path "$basePath/data/repositories" -Force | Out-Null
    
    # Create domain layer directories
    New-Item -ItemType Directory -Path "$basePath/domain/entities" -Force | Out-Null
    New-Item -ItemType Directory -Path "$basePath/domain/repositories" -Force | Out-Null
    New-Item -ItemType Directory -Path "$basePath/domain/usecases" -Force | Out-Null
    
    # Create presentation layer directories
    New-Item -ItemType Directory -Path "$basePath/presentation/bloc" -Force | Out-Null
    New-Item -ItemType Directory -Path "$basePath/presentation/pages" -Force | Out-Null
    New-Item -ItemType Directory -Path "$basePath/presentation/widgets" -Force | Out-Null
    
    Write-Host "Created structure for $feature feature"
}

Write-Host "All feature structures created successfully!"
