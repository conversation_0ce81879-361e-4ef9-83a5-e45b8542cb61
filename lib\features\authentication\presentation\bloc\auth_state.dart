import 'package:equatable/equatable.dart';

/// Base authentication state
abstract class AuthState extends Equatable {
  const AuthState();
  
  @override
  List<Object?> get props => [];
}

/// Initial authentication state
class AuthInitial extends AuthState {
  const AuthInitial();
}

/// Loading state during authentication operations
class AuthLoading extends AuthState {
  const AuthLoading();
}

/// User is authenticated
class AuthAuthenticated extends AuthState {
  final String userId;
  
  const AuthAuthenticated({required this.userId});
  
  @override
  List<Object?> get props => [userId];
}

/// User is not authenticated
class AuthUnauthenticated extends AuthState {
  const AuthUnauthenticated();
}

/// User registration completed
class AuthRegistered extends AuthState {
  const AuthRegistered();
}

/// PIN verification completed
class AuthPinVerified extends AuthState {
  const AuthPinVerified();
}

/// Authentication error occurred
class AuthError extends AuthState {
  final String message;
  
  const AuthError(this.message);
  
  @override
  List<Object?> get props => [message];
}
