import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl_phone_field/country_picker_dialog.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:intl_phone_field/phone_number.dart';
import 'package:Kairos/widgets/snackbar_widget.dart';
class PhoneAuthentication extends StatefulWidget{
  const PhoneAuthentication({super.key, required this.pageController});
  final PageController pageController;

  @override
  State<PhoneAuthentication> createState() => _PhoneAuthenticationState();
}


class _PhoneAuthenticationState extends State<PhoneAuthentication>{

  final TextEditingController _textEditingController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  late PhoneNumber? _phoneNumber;
  late String? fullPhoneNumber = "";
  bool _isLoading = false; // Added state variable for spinner


  @override
  void dispose(){
    super.dispose();
    _textEditingController.dispose();
  }

  @override
  Widget build(BuildContext context){
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
      Text("AUTHENTIFICATION", style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Theme.of(context).primaryColor)),
      SvgPicture.asset(
        "assets/images/logo_kairos.svg",
        ),
      Divider(color: Theme.of(context).primaryColor, thickness: 5, height: 20, indent: 100, endIndent: 100,),
      Spacer(),
      Flexible(flex: 8, child: SvgPicture.asset("assets/images/phone_auth.svg")),
      // Spacer(flex: 2),
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        child: Text("Veuillez saisir votre numéro de téléphone pour rececoir votre code d'activation.", textAlign: TextAlign.center),
      ),
      // SizedBox(height: 20),
      Spacer(),
      Form(
        key: _formKey,
        child: SizedBox(
        width: 300,
        child: IntlPhoneField(
                  controller: _textEditingController,
                  decoration: InputDecoration(
                      border: OutlineInputBorder(),
                      hintText: "77 123 45 67",
                      hintStyle: TextStyle(color: Colors.grey.shade400),
                      contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                      ),
                  initialCountryCode: 'SN',
                  flagsButtonPadding: EdgeInsets.only(left: 10),
                  keyboardType: TextInputType.phone, 
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                  ],
                  pickerDialogStyle: PickerDialogStyle(
                    
                    searchFieldInputDecoration: InputDecoration(
                      hintText: "Rechercher un pays",
                      hintStyle: TextStyle(color: Colors.grey.shade400),
                      border: OutlineInputBorder(borderRadius: BorderRadius.circular(1.0)),
                      suffixIcon: Icon(Icons.search),
                    ),
                    
                  ),
                  dropdownIconPosition: IconPosition.trailing,
                  dropdownTextStyle: TextStyle(color: Colors.black),
                  validator: (value){
                    debugPrint("Phone number: $value");
                    if(value!.isValidNumber() == false){
                      return "Veuillez saisir un numéro de téléphone valide";
                    } else if(value.completeNumber.isEmpty){
                      return "Veuillez saisir votre numéro de téléphone";
                    } else {
                      return null;
                    }
                  },
                  onChanged: (PhoneNumber phoneNumber){
                    _phoneNumber = phoneNumber;
                    debugPrint("Phone number changed: $phoneNumber");
                    
                  },
                  
      ),),),
      FilledButton(
        style: ButtonStyle(
            backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
            fixedSize: WidgetStateProperty.all(Size(300, 50))),
        onPressed: _isLoading ? null : () async { // Disable button while loading
          debugPrint('the user clicked on `Continue` button');
          if(_formKey.currentState!.validate()){
            setState(() {
              _isLoading = true; // Show spinner
            });
            debugPrint('the user entered: ${_textEditingController.text}');

            // Simulate HTTP request delay
            await Future.delayed(Duration(seconds: 2));

            setState(() {
              _isLoading = false; // Hide spinner
              ScaffoldMessenger.of(context).showSnackBar(CustomSnackbar(message:"Code d'activation envoyé au numéro: $fullPhoneNumber").getSnackBar());
            });

            widget.pageController.nextPage(duration: Duration(milliseconds: 500), curve: Curves.easeInOut);
            fullPhoneNumber = _phoneNumber!.completeNumber;
          } else {
            ScaffoldMessenger.of(context).showSnackBar(CustomSnackbar(message: "Veuillez saisir un numéro de téléphone valide").getSnackBar());
          }
          },
        child: _isLoading
            ? SizedBox( // Show spinner when loading
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              )
            : Text("SUIVANT"), // Show text when not loading
    ),
      Spacer(flex: 2),
      ]);
  }
}