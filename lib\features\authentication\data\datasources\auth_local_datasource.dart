import '../models/user_info.dart';

/// Abstract interface for authentication local data source
abstract class AuthLocalDataSource {
  /// Cache user data locally
  Future<void> cacheUser(UserInfoModel user);
  
  /// Get cached user data
  Future<UserInfoModel> getCachedUser();
  
  /// Check if user is authenticated
  Future<bool> isAuthenticated();
  
  /// Clear all cached data
  Future<void> clearCache();
  
  /// Cache authentication token
  Future<void> cacheToken(String token);
  
  /// Get cached authentication token
  Future<String?> getCachedToken();
}

/// Implementation of AuthLocalDataSource
class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  // TODO: Inject SharedPreferences
  
  @override
  Future<void> cacheUser(UserInfoModel user) async {
    // TODO: Implement local storage
    throw UnimplementedError('Cache user not implemented yet');
  }
  
  @override
  Future<UserInfoModel> getCachedUser() async {
    // TODO: Implement local storage retrieval
    throw UnimplementedError('Get cached user not implemented yet');
  }
  
  @override
  Future<bool> isAuthenticated() async {
    // TODO: Check if user is authenticated
    throw UnimplementedError('Is authenticated check not implemented yet');
  }
  
  @override
  Future<void> clearCache() async {
    // TODO: Clear all cached data
    throw UnimplementedError('Clear cache not implemented yet');
  }
  
  @override
  Future<void> cacheToken(String token) async {
    // TODO: Cache authentication token
    throw UnimplementedError('Cache token not implemented yet');
  }
  
  @override
  Future<String?> getCachedToken() async {
    // TODO: Get cached authentication token
    throw UnimplementedError('Get cached token not implemented yet');
  }
}
