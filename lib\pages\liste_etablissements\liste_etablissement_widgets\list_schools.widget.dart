import 'package:flutter/material.dart';
import 'package:Kairos/data/models/school_entry.dart';

class ListSchoolsWidget extends StatelessWidget {
  const ListSchoolsWidget({super.key});

  final List<SchoolEntry> mockSchools = const [
    SchoolEntry(
      name: "<PERSON><PERSON> Dakar",
      profile: "ETUDIANT",
      id: "1234",
      logoPath: "assets/images/logo_iam.png",
    ),
    SchoolEntry(
      name: "IAM Saint-Louis",
      profile: "PARENT",
      id: "5678",
      logoPath: "assets/images/logo_iam.png",
    ),
    SchoolEntry(
      name: "IAM Thiès",
      profile: "TUTEUR",
      id: "9012",
      logoPath: "assets/images/logo_iam.png",
    ),
    SchoolEntry(
      name: "IAM Kaolack",
      profile: "ETUDIANT",
      id: "3456",
      logoPath: "assets/images/logo_iam.png",
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      itemCount: mockSchools.length,
      itemBuilder: (context, index) {
        final school = mockSchools[index];
        return Card(
          color: Theme.of(context).scaffoldBackgroundColor,
          child: InkWell(
            onTap: () {
              if (school.profile == "PARENT" || school.profile == "TUTEUR") {
                Navigator.pushNamed(context, "/dossier_selection");
              } else {
                Navigator.pushNamed(context, "/dashboard");
              }
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 1.0),
              child: ListTile(
                leading: Image.asset(school.logoPath ?? "assets/images/logo_iam.png"),
                title: Text(school.name),
                subtitle: Text.rich(
                  TextSpan(
                    children: [
                      const TextSpan(
                        text: "PROFILE: ",
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      TextSpan(text: "${school.profile} "),
                      const TextSpan(
                        text: "ID: ",
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      TextSpan(text: school.id),
                    ],
                  ),
                ),
                trailing: const Icon(Icons.arrow_forward_ios, size: 10),
              ),
            ),
          ),
        );
      },
    );
  }
}