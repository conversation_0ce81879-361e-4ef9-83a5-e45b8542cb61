import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:Kairos/pages/absences_retards/absences_retards.page.dart';
import 'package:Kairos/pages/cahier_texte/cahier_texte.page.dart';
import 'package:Kairos/pages/dossiers_etudiant/dossiers.page.dart';
import 'package:Kairos/pages/emploi_du_temps/emploi_du_temps.page.dart';
import 'package:Kairos/pages/finances/finances.page.dart';
// import 'package:Kairos/pages/liste_etablissements/liste_etablissement_widgets/alert.widget.dart';
import 'package:Kairos/pages/notes/notes.page.dart';
import 'package:Kairos/widgets/snackbar_widget.dart';
import 'package:Kairos/constants/dashboard_strings.dart';

class DashboardItem extends StatelessWidget{
  final dynamic title;
  final dynamic subtitle;
  final dynamic iconName;

  const DashboardItem({super.key, required this.title, this.subtitle, required this.iconName});

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Theme.of(context).scaffoldBackgroundColor,
      shadowColor: Colors.black,
      child: ListTile(
        title: Padding(
          padding: const EdgeInsets.symmetric(vertical: 5),
          child: ListTile(
            dense: true,
            title: Text(title, style: TextStyle(fontSize: 14)),
            subtitle: subtitle != null ? Text(subtitle) : null,
          ),
        ),
        leading: SvgPicture.asset("assets/icons/$iconName", width: 20, height: 20,),
        trailing: Icon(Icons.arrow_forward_ios, size: 10,),
        onTap: () {
          switch(title.toString()) {
            case DashboardStrings.notesTitle:
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => NotesPage()),
              );
              break;
            case DashboardStrings.financesTitle:
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => FinancesPage()),
              );
              // showDialog(context: context, 
              //           builder: (context) => AlertWidget(message: "Vous avez des impayés ! Veuillez régulariser votre situation avant de continuer.")
              //           .build(context));
              break;
            case DashboardStrings.absencesTitle:
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => AbsencesRetardsPage()),
              );
              break;
            case DashboardStrings.dossiersTitle:
              Navigator.push(context,
              MaterialPageRoute(builder: (context) => DossiersPage()));
              break;
            case DashboardStrings.cahierTitle:
               Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => CahierTextePage()),
              );
              break;
            case DashboardStrings.planningTitle:
               Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => EmploiDuTempsPage()),
              );
              break;

            default :
               ScaffoldMessenger.of(context).showSnackBar(
              CustomSnackbar(message: "Fonctionnalité en cours de développement").getSnackBar()
            );
              break;
          }
        },
      ),
    );
  }
}
