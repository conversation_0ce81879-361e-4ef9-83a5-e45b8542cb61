import 'package:get_it/get_it.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../api/api_client.dart';
import '../network/network_info.dart';
import '../services/device_info_service.dart';

/// Service locator instance
final sl = GetIt.instance;

/// Initialize all dependencies
Future<void> init() async {
  //! Features - Authentication
  // TODO: Register authentication dependencies
  
  //! Features - Dashboard
  // TODO: Register dashboard dependencies
  
  //! Features - Finances
  // TODO: Register finances dependencies
  
  //! Features - Grades
  // TODO: Register grades dependencies
  
  //! Features - Schedule
  // TODO: Register schedule dependencies
  
  //! Features - Absences
  // TODO: Register absences dependencies
  
  //! Features - Course Log
  // TODO: Register course log dependencies
  
  //! Features - Student Records
  // TODO: Register student records dependencies
  
  //! Features - Profile
  // TODO: Register profile dependencies
  
  //! Features - Notifications
  // TODO: Register notifications dependencies
  
  //! Features - Schools
  // TODO: Register schools dependencies
  
  //! Features - Splash
  // TODO: Register splash dependencies
  
  //! Core
  sl.registerLazySingleton<ApiClient>(() => ApiClient());
  sl.registerLazySingleton<NetworkInfo>(() => NetworkInfoImpl(sl()));
  sl.registerLazySingleton(() => DeviceInfoService());
  
  //! External
  final sharedPreferences = await SharedPreferences.getInstance();
  sl.registerLazySingleton(() => sharedPreferences);
  sl.registerLazySingleton(() => Connectivity());
}

/// Register feature-specific dependencies
/// This method will be called for each feature as they are implemented
void registerFeatureDependencies() {
  // TODO: Implement feature-specific dependency registration
}
