import 'package:Kairos/core/widgets/common/empty_message.widget.dart';
import 'package:Kairos/core/widgets/layout/custom_app_bar.widget.dart';
import 'package:Kairos/core/widgets/indicators/custom_spinner.dart';
import 'package:Kairos/core/widgets/inputs/search_bar_sliver.widget.dart';
import 'package:Kairos/core/enums/header_enums.dart';
import 'package:Kairos/core/utils/date_utils.dart';
import 'package:flutter/material.dart';
import 'package:Kairos/data/models/course_log.dart';
import 'package:Kairos/pages/cahier_texte/cahier_texte_widgets/course_log_item.widget.dart';

class CahierTextePage extends StatefulWidget {
  const CahierTextePage({super.key});

  @override
  State<CahierTextePage> createState() => _CahierTextePageState();
}

class _CahierTextePageState extends State<CahierTextePage> with SingleTickerProviderStateMixin {
  bool _isLoading = true;
  bool _isSearching = false;
  final TextEditingController _searchController = TextEditingController();
  late List<CourseLogEntry> _filteredCourseLogs;
  String? _startDate;
  String? _endDate;

  final List<CourseLogEntry> courseLogs = [
    CourseLogEntry(
      day: 'MER',
      date: '23/11',
      timeRange: '07:30 - 10:00',
      teacher: 'Thiate BARRY',
      subject: 'Introduction à la communication',
      registeredBy: 'Thiate BARRY',
      registeredDate: '23/11/2022',
      courseCode: 'PALT_C1B_2021',
      semester: 'Semestre 1',
      duration: '2H30mn',
    ),
    CourseLogEntry(
      day: 'MAR',
      date: '22/11',
      timeRange: '09:00 - 11:00',
      teacher: 'Thiate BARRY',
      subject: 'Introduction à la communication',
      registeredBy: 'Thiate BARRY',
      registeredDate: '22/11/2022',
      courseCode: 'PALT_C1B_2021',
      semester: 'Semestre 1',
      duration: '2H',
    ),
    CourseLogEntry(
      day: 'MAR',
      date: '22/11',
      timeRange: '13:00 - 15:30',
      teacher: 'Thiate BARRY',
      subject: 'Introduction à la communication',
      registeredBy: 'Thiate BARRY',
      registeredDate: '22/11/2022',
      courseCode: 'PALT_C1B_2021',
      semester: 'Semestre 1',
      duration: '2H30',
    ),
    CourseLogEntry(
      day: 'MAR',
      date: '07/06',
      timeRange: '08:30 - 09:30',
      teacher: 'Thiate BARRY',
      subject: 'Introduction à la communication',
      registeredBy: 'SARR Mouhamadou A',
      registeredDate: '07/06/2022',
      courseCode: 'PALT_C1B_2021',
      semester: 'Semestre 1',
      duration: '1H',
    ),
  ];

  late AnimationController _searchAnimationController;

  @override
  void initState() {
    super.initState();
    _filteredCourseLogs = courseLogs;
    _searchController.addListener(_filterCourseLogs);
    _searchAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 100),
    );

    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchAnimationController.dispose();
    super.dispose();
  }

  void _filterCourseLogs() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredCourseLogs = courseLogs.where((log) {

        bool matchesText = log.day.toLowerCase().contains(query) ||
               log.date.toLowerCase().contains(query) ||
               log.timeRange.toLowerCase().contains(query) ||
               log.teacher.toLowerCase().contains(query) ||
               log.subject.toLowerCase().contains(query) ||
               log.registeredBy.toLowerCase().contains(query) ||
               log.registeredDate.toLowerCase().contains(query) ||
               log.courseCode.toLowerCase().contains(query) ||
               log.semester.toLowerCase().contains(query);

        bool matchesDateRange = true;
        if (_startDate != null && _endDate != null) {
          matchesDateRange = _startDate!.isNotEmpty && _endDate!.isNotEmpty
              ? isDateInRange(log.registeredDate, _startDate!, _endDate!)
              : true;
        }

        return matchesText && matchesDateRange;
      }).toList();
    });
  }



  // Method to handle date filter changes
  void _onDateFilterChanged(Map<String, String> dateRange) {
    setState(() {
      _startDate = dateRange['startDate'] ?? '';
      _endDate = dateRange['endDate'] ?? '';
    });
    _filterCourseLogs();
  }


  void _onClearDateFilter() {
    setState(() {
      _startDate = null;
      _endDate = null;
    });
    _filterCourseLogs();
  }


  void _toggleSearch() {
    setState(() {
      _isSearching = !_isSearching;
      if (_isSearching) {
        _searchAnimationController.forward();
      } else {
        _searchAnimationController.reverse();
        _searchController.clear();
        _startDate = null;
        _endDate = null;
        _filterCourseLogs();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: false,
      body: CustomScrollView(
        slivers: [
          CustomAppBar(
            pageSection: HeaderEnum.cahierDeTexte,
            title: "CAHIER DE TEXTE",
            isSearchBarVisible: _isSearching, // Control visibility of search bar
            onSearchTap: _toggleSearch, // Pass the toggle method
          ),
          AnimatedBuilder(
            animation: _searchAnimationController,
            builder: (context, child) {
              return SliverPersistentHeader(
                delegate: SearchBarSliver(
                  extentHeight: _searchAnimationController.value * 60.0, // Animate height
                  searchController: _searchController,
                  onSearchChanged: (query) => _filterCourseLogs(),
                  onDateFilterChanged: _onDateFilterChanged,
                  hasActiveFilter: _startDate != null && _endDate != null,
                  onClearDateFilter: _onClearDateFilter,
                  hintText: "Rechercher un cours...",
                ),
              );
            },
          ),
          _isLoading
          ? SliverFillRemaining(
              child: Center(
                child: CustomSpinner(
                  size: 60.0,
                  strokeWidth: 5.0,
                ),
              ),
            )
          : SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final logEntry = _filteredCourseLogs[index]; // Use filtered list
                  return CourseLogItem(logEntry: logEntry);
                },
                childCount: _filteredCourseLogs.length, // Use filtered list length
              ),
            ),
        ]
      )
    );
  }
}
