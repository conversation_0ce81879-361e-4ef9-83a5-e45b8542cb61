import 'package:flutter/material.dart';
import 'package:Kairos/data/models/dossier.dart'; // Import the Dossier model
import 'package:Kairos/pages/dossiers_etudiant/dossiers_widgets/dossier_details_dialog.widget.dart'; // Import the dialog widget

class DossierItem extends StatelessWidget {
  final Dossier dossier;

  const DossierItem({super.key, required this.dossier});

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: ListTile(
        leading: CircleAvatar(
          radius: 30,
          backgroundColor: Theme.of(context).primaryColor, // Use the app's primary color
          child: Text(
            dossier.initials,
            style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              dossier.title,
              style: TextStyle(fontSize: 14, color: Theme.of(context).primaryColor), // Use the app's primary color
            ),
            Text(
              dossier.boldTitle,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        subtitle: Text(dossier.description, overflow: TextOverflow.ellipsis),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.attach_file, size: 10, color: Colors.grey),
                const SizedBox(width: 4),
                Text(
                  dossier.date,
                  style: const TextStyle(fontSize: 10, color: Colors.grey),
                ),
              ],
            ),
          ],
        ),
        onTap: () {
          debugPrint('the user clicked on ${dossier.title}');
          showDialog(
            context: context,
            builder: (context) => DossierDetailsDialogWidget(dossier: dossier),
          );
        },
      ),
    );
  }
}
