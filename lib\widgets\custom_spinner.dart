import 'package:flutter/material.dart';

class CustomSpinner extends StatelessWidget {
  final double size;
  final double strokeWidth;
  
  const CustomSpinner({
    super.key, 
    this.size = 40.0, 
    this.strokeWidth = 4.0,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: size,
      width: size,
      child: CircularProgressIndicator(
        color: Theme.of(context).primaryColor,
        strokeWidth: strokeWidth,
      ),
    );
  }
}